/**
 * 词库管理核心逻辑
 * 🎯 核心价值：统一的词库数据管理，支持29个颜色级别组合的词库系统
 * 📦 功能范围：词库创建、词语管理、验证检测、重复处理
 * 🔄 架构设计：基于Map的高性能数据结构，支持实时验证和同步
 */

import type {
  BasicColorType,
  DataLevel,
  WordEntry,
  WordLibrary,
  WordLibraryKey,
  WordLibraryState,
  WordValidationResult
} from '../matrix/MatrixTypes';

import { createWordLibraryKey } from '../matrix/MatrixTypes';

import { AVAILABLE_LEVELS } from '../data/GroupAData';

// ===== 常量定义 =====

/** 词语长度限制 */
export const WORD_LENGTH_LIMITS = {
  MIN: 2,
  MAX: 4
} as const;

/** 支持的颜色顺序（按UI显示顺序） */
export const COLOR_DISPLAY_ORDER: BasicColorType[] = [
  'black', 'red', 'orange', 'yellow', 'green', 'cyan', 'blue', 'purple', 'pink'
];

/** 所有可用的词库配置 */
export const AVAILABLE_WORD_LIBRARIES: Array<{ color: BasicColorType; level: DataLevel }> = [];

// 初始化可用词库列表
COLOR_DISPLAY_ORDER.forEach(color => {
  const availableLevels = AVAILABLE_LEVELS[color];
  availableLevels.forEach(level => {
    AVAILABLE_WORD_LIBRARIES.push({ color, level: level as DataLevel });
  });
});

// ===== 词库工具函数 =====

/**
 * 创建空词库
 */
export const createEmptyWordLibrary = (color: BasicColorType, level: DataLevel): WordLibrary => ({
  key: createWordLibraryKey(color, level),
  color,
  level,
  words: [],
  collapsed: false,
  lastUpdated: new Date()
});

/**
 * 创建词语条目
 */
export const createWordEntry = (text: string, color: BasicColorType, level: DataLevel): WordEntry => ({
  id: generateWordId(),
  text: text.trim(),
  color,
  level,
  createdAt: new Date(),
  updatedAt: new Date(),
  usageCount: 0,
  usagePositions: []
});

/**
 * 生成词语唯一ID
 */
export const generateWordId = (): string => {
  return `word_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * 验证词语格式
 */
export const validateWordText = (text: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  const trimmedText = text.trim();

  // 检查长度
  if (trimmedText.length < WORD_LENGTH_LIMITS.MIN) {
    errors.push(`词语长度不能少于${WORD_LENGTH_LIMITS.MIN}个字符`);
  }
  if (trimmedText.length > WORD_LENGTH_LIMITS.MAX) {
    errors.push(`词语长度不能超过${WORD_LENGTH_LIMITS.MAX}个字符`);
  }

  // 检查是否为空
  if (!trimmedText) {
    errors.push('词语不能为空');
  }

  // 检查是否包含中文字符
  const chineseRegex = /[\u4e00-\u9fff]/;
  if (!chineseRegex.test(trimmedText)) {
    errors.push('词语必须包含中文字符');
  }

  // 检查是否包含特殊字符
  const specialCharsRegex = /[，。！？；：""''（）【】《》]/;
  if (specialCharsRegex.test(trimmedText)) {
    errors.push('词语不能包含标点符号');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * 检查词语重复
 */
export const checkWordDuplicate = (
  text: string,
  currentLibraryKey: WordLibraryKey,
  libraries: Map<WordLibraryKey, WordLibrary>
): { isDuplicate: boolean; duplicateLibraries: WordLibraryKey[] } => {
  const trimmedText = text.trim();
  const duplicateLibraries: WordLibraryKey[] = [];

  libraries.forEach((library, key) => {
    const hasWord = library.words.some(word => word.text === trimmedText);
    if (hasWord) {
      duplicateLibraries.push(key);
    }
  });

  return {
    isDuplicate: duplicateLibraries.length > 0,
    duplicateLibraries
  };
};

/**
 * 完整的词语验证
 */
export const validateWord = (
  text: string,
  libraryKey: WordLibraryKey,
  libraries: Map<WordLibraryKey, WordLibrary>
): WordValidationResult => {
  const formatValidation = validateWordText(text);
  const duplicateCheck = checkWordDuplicate(text, libraryKey, libraries);

  return {
    isValid: formatValidation.isValid,
    errors: formatValidation.errors,
    isDuplicate: duplicateCheck.isDuplicate,
    duplicateLibraries: duplicateCheck.duplicateLibraries
  };
};

/**
 * 解析输入文本为词语数组
 */
export const parseInputText = (input: string): string[] => {
  return input
    .split(/[，,]/) // 支持中英文逗号分割
    .map(word => word.trim())
    .filter(word => word.length > 0);
};

/**
 * 初始化词库状态
 */
export const createInitialWordLibraryState = (): WordLibraryState => {
  const libraries = new Map<WordLibraryKey, WordLibrary>();

  // 创建所有可用的词库
  AVAILABLE_WORD_LIBRARIES.forEach(({ color, level }) => {
    const library = createEmptyWordLibrary(color, level);

    // 为黑色1级词库添加默认测试数据
    if (color === 'black' && level === 1) {
      const defaultWord = createWordEntry('主题', color, level);
      library.words.push(defaultWord);
      library.lastUpdated = new Date();
    }

    libraries.set(library.key, library);
  });

  return {
    libraries,
    activeLibrary: null,
    duplicateWords: new Map(),
    isLoading: false,
    lastSyncTime: null
  };
};

/**
 * 获取词库显示名称
 */
export const getWordLibraryDisplayName = (color: BasicColorType, level: DataLevel): string => {
  const colorNames: Record<BasicColorType, string> = {
    black: '黑色',
    red: '红色',
    orange: '橙色',
    yellow: '黄色',
    green: '绿色',
    cyan: '青色',
    blue: '蓝色',
    purple: '紫色',
    pink: '粉色'
  };

  return `${colorNames[color]}${level}级`;
};

/**
 * 获取词库背景色
 */
export const getWordLibraryBackgroundColor = (color: BasicColorType): string => {
  const colorMap: Record<BasicColorType, string> = {
    black: '#000000',
    red: '#ef4444',
    orange: '#f97316',
    yellow: '#eab308',
    green: '#22c55e',
    cyan: '#06b6d4',
    blue: '#3b82f6',
    purple: '#a855f7',
    pink: '#ec4899'
  };

  return colorMap[color];
};

/**
 * 获取文字颜色（基于背景色自动适配）
 */
export const getWordLibraryTextColor = (backgroundColor: string): string => {
  // 简单的亮度检测
  const isDark = backgroundColor === '#000000' ||
    (backgroundColor.startsWith('#') && parseInt(backgroundColor.slice(1), 16) < 0x808080);

  return isDark ? '#ffffff' : '#000000';
};

/**
 * 更新重复词语映射
 */
export const updateDuplicateWordsMap = (libraries: Map<WordLibraryKey, WordLibrary>): Map<string, WordLibraryKey[]> => {
  const duplicateMap = new Map<string, WordLibraryKey[]>();

  // 收集所有词语
  const wordMap = new Map<string, WordLibraryKey[]>();

  libraries.forEach((library, key) => {
    library.words.forEach(word => {
      if (!wordMap.has(word.text)) {
        wordMap.set(word.text, []);
      }
      wordMap.get(word.text)!.push(key);
    });
  });

  // 找出重复的词语
  wordMap.forEach((libraryKeys, wordText) => {
    if (libraryKeys.length > 1) {
      duplicateMap.set(wordText, libraryKeys);
    }
  });

  return duplicateMap;
};
