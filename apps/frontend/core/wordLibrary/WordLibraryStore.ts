/**
 * 词库管理状态存储
 * 🎯 核心价值：统一的词库状态管理，支持持久化和实时同步
 * 📦 功能范围：词库状态、词语管理、填词模式、数据持久化
 * 🔄 架构设计：基于Zustand的响应式状态管理，支持本地存储
 */

import { enableMapSet, produce } from 'immer';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import type {
  BasicColorType,
  DataLevel,
  WordEntry,
  WordInputState,
  WordLibrary,
  WordLibraryKey,
  WordLibraryState,
  WordValidationResult
} from '../matrix/MatrixTypes';

import {
  createInitialWordLibraryState,
  createWordEntry,
  updateDuplicateWordsMap,
  validateWord
} from './WordLibraryCore';

import { createWordLibraryKey } from '../matrix/MatrixTypes';

// 启用 Immer 的 MapSet 插件
enableMapSet();

// ===== Store 接口定义 =====

interface WordLibraryStore extends WordLibraryState {
  // ===== 词库管理 =====

  /** 添加词语到指定词库 */
  addWord: (libraryKey: WordLibraryKey, text: string) => WordValidationResult;

  /** 从词库中删除词语 */
  removeWord: (libraryKey: WordLibraryKey, wordId: string) => boolean;

  /** 更新词语文本 */
  updateWord: (libraryKey: WordLibraryKey, wordId: string, newText: string) => WordValidationResult;

  /** 批量添加词语 */
  addWords: (libraryKey: WordLibraryKey, texts: string[]) => WordValidationResult[];

  /** 清空词库 */
  clearLibrary: (libraryKey: WordLibraryKey) => void;

  /** 切换词库折叠状态 */
  toggleLibraryCollapse: (libraryKey: WordLibraryKey) => void;

  /** 设置活跃词库 */
  setActiveLibrary: (libraryKey: WordLibraryKey | null) => void;

  // ===== 词语查询 =====

  /** 获取词库 */
  getLibrary: (libraryKey: WordLibraryKey) => WordLibrary | undefined;

  /** 获取词语 */
  getWord: (libraryKey: WordLibraryKey, wordId: string) => WordEntry | undefined;

  /** 搜索词语 */
  searchWords: (query: string) => Array<{ word: WordEntry; libraryKey: WordLibraryKey }>;

  /** 获取匹配的词库（根据颜色和级别） */
  getMatchingLibrary: (color: BasicColorType, level: DataLevel) => WordLibrary | undefined;

  // ===== 验证和检测 =====

  /** 验证词语 */
  validateWordText: (libraryKey: WordLibraryKey, text: string) => WordValidationResult;

  /** 更新重复词语映射 */
  updateDuplicateMap: () => void;

  /** 获取重复词语信息 */
  getDuplicateInfo: (text: string) => WordLibraryKey[] | undefined;

  /** 获取词语高亮颜色 */
  getWordHighlightColor: (word: string) => string | undefined;

  /** 设置词语高亮颜色 */
  setWordHighlightColor: (word: string, color: string) => void;

  /** 清除词语高亮颜色 */
  clearWordHighlightColor: (word: string) => void;

  // ===== 数据管理 =====

  /** 导出词库数据 */
  exportData: () => string;

  /** 导入词库数据 */
  importData: (data: string) => boolean;

  /** 重置所有词库 */
  resetAllLibraries: () => void;

  /** 获取统计信息 */
  getStatistics: () => {
    totalLibraries: number;
    totalWords: number;
    duplicateWords: number;
    libraryStats: Array<{ libraryKey: WordLibraryKey; wordCount: number }>;
  };
}

// ===== 填词模式状态管理 =====

interface WordInputStore extends WordInputState {
  /** 激活填词模式 */
  activateWordInput: (x: number, y: number, color: BasicColorType, level: DataLevel) => void;

  /** 退出填词模式 */
  deactivateWordInput: () => void;

  /** 选择下一个词语 */
  selectNextWord: () => void;

  /** 选择上一个词语 */
  selectPreviousWord: () => void;

  /** 确认选择当前词语 */
  confirmWordSelection: () => WordEntry | null;

  /** 删除当前单元格的词语 */
  clearCellWord: () => void;
}

// ===== 创建词库管理Store =====

export const useWordLibraryStore = create<WordLibraryStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      ...createInitialWordLibraryState(),

      // ===== 词库管理方法 =====

      addWord: (libraryKey: WordLibraryKey, text: string) => {
        const state = get();
        const validation = validateWord(text, libraryKey, state.libraries);

        if (validation.isValid) {
          set(produce((draft) => {
            const library = draft.libraries.get(libraryKey);
            if (library) {
              const wordEntry = createWordEntry(text, library.color, library.level);
              library.words.push(wordEntry);
              library.lastUpdated = new Date();
            }
          }));

          // 更新重复词语映射
          get().updateDuplicateMap();
        }

        return validation;
      },

      removeWord: (libraryKey: WordLibraryKey, wordId: string) => {
        let removed = false;

        set(produce((draft) => {
          const library = draft.libraries.get(libraryKey);
          if (library) {
            const wordIndex = library.words.findIndex((word: WordEntry) => word.id === wordId);
            if (wordIndex !== -1) {
              library.words.splice(wordIndex, 1);
              library.lastUpdated = new Date();
              removed = true;
            }
          }
        }));

        if (removed) {
          get().updateDuplicateMap();
        }

        return removed;
      },

      updateWord: (libraryKey: WordLibraryKey, wordId: string, newText: string) => {
        const state = get();
        const validation = validateWord(newText, libraryKey, state.libraries);

        if (validation.isValid) {
          set(produce((draft) => {
            const library = draft.libraries.get(libraryKey);
            if (library) {
              const word = library.words.find((w: WordEntry) => w.id === wordId);
              if (word) {
                word.text = newText.trim();
                word.updatedAt = new Date();
                library.lastUpdated = new Date();
              }
            }
          }));

          get().updateDuplicateMap();
        }

        return validation;
      },

      addWords: (libraryKey: WordLibraryKey, texts: string[]) => {
        const results: WordValidationResult[] = [];

        texts.forEach(text => {
          const result = get().addWord(libraryKey, text);
          results.push(result);
        });

        return results;
      },

      clearLibrary: (libraryKey: WordLibraryKey) => {
        set(produce((draft) => {
          const library = draft.libraries.get(libraryKey);
          if (library) {
            library.words = [];
            library.lastUpdated = new Date();
          }
        }));

        get().updateDuplicateMap();
      },

      toggleLibraryCollapse: (libraryKey: WordLibraryKey) => {
        set(produce((draft) => {
          const library = draft.libraries.get(libraryKey);
          if (library) {
            library.collapsed = !library.collapsed;
          }
        }));
      },

      setActiveLibrary: (libraryKey: WordLibraryKey | null) => {
        set({ activeLibrary: libraryKey });
      },

      // ===== 查询方法 =====

      getLibrary: (libraryKey: WordLibraryKey) => {
        return get().libraries.get(libraryKey);
      },

      getWord: (libraryKey: WordLibraryKey, wordId: string) => {
        const library = get().libraries.get(libraryKey);
        return library?.words.find(word => word.id === wordId);
      },

      searchWords: (query: string) => {
        const results: Array<{ word: WordEntry; libraryKey: WordLibraryKey }> = [];
        const state = get();

        state.libraries.forEach((library, libraryKey) => {
          library.words.forEach(word => {
            if (word.text.includes(query)) {
              results.push({ word, libraryKey });
            }
          });
        });

        return results;
      },

      getMatchingLibrary: (color: BasicColorType, level: DataLevel) => {
        const libraryKey = createWordLibraryKey(color, level);
        return get().libraries.get(libraryKey);
      },

      // ===== 验证方法 =====

      validateWordText: (libraryKey: WordLibraryKey, text: string) => {
        const state = get();
        return validateWord(text, libraryKey, state.libraries);
      },

      updateDuplicateMap: () => {
        const state = get();
        const duplicateMap = updateDuplicateWordsMap(state.libraries);
        set({ duplicateWords: duplicateMap });
      },

      getDuplicateInfo: (text: string) => {
        return get().duplicateWords.get(text);
      },

      // ===== 数据管理方法 =====

      exportData: () => {
        const state = get();
        const exportData = {
          libraries: Array.from(state.libraries.entries()),
          lastSyncTime: state.lastSyncTime,
          exportTime: new Date()
        };
        return JSON.stringify(exportData, null, 2);
      },

      importData: (data: string) => {
        try {
          const importData = JSON.parse(data);
          const libraries = new Map(importData.libraries) as Map<WordLibraryKey, WordLibrary>;

          set({
            libraries,
            lastSyncTime: new Date(),
            duplicateWords: updateDuplicateWordsMap(libraries)
          });

          return true;
        } catch (error) {
          console.error('导入数据失败:', error);
          return false;
        }
      },

      resetAllLibraries: () => {
        set(createInitialWordLibraryState());
      },

      // ===== 随机颜色管理方法 =====

      getWordHighlightColor: (word: string) => {
        const state = get();
        return state.wordHighlightColors.get(word);
      },

      setWordHighlightColor: (word: string, color: string) => {
        set(produce((draft) => {
          draft.wordHighlightColors.set(word, color);
          draft.usedHighlightColors.add(color);
        }));
      },

      clearWordHighlightColor: (word: string) => {
        set(produce((draft) => {
          const color = draft.wordHighlightColors.get(word);
          if (color) {
            draft.wordHighlightColors.delete(word);
            // 检查是否还有其他词语使用这个颜色
            const isColorStillUsed = Array.from(draft.wordHighlightColors.values()).includes(color);
            if (!isColorStillUsed) {
              draft.usedHighlightColors.delete(color);
            }
          }
        }));
      },

      getStatistics: () => {
        const state = get();
        const libraryStats: Array<{ libraryKey: WordLibraryKey; wordCount: number }> = [];
        let totalWords = 0;

        state.libraries.forEach((library, libraryKey) => {
          const wordCount = library.words.length;
          libraryStats.push({ libraryKey, wordCount });
          totalWords += wordCount;
        });

        return {
          totalLibraries: state.libraries.size,
          totalWords,
          duplicateWords: state.duplicateWords.size,
          libraryStats
        };
      }
    }),
    {
      name: 'word-library-storage',
      version: 1,
      partialize: (state) => ({
        libraries: Array.from(state.libraries.entries()),
        activeLibrary: state.activeLibrary,
        duplicateWords: Array.from(state.duplicateWords.entries()),
        wordHighlightColors: Array.from(state.wordHighlightColors.entries()),
        usedHighlightColors: Array.from(state.usedHighlightColors),
        isLoading: state.isLoading,
        lastSyncTime: state.lastSyncTime
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          // 重新构建Map和Set对象
          if (Array.isArray(state.libraries)) {
            state.libraries = new Map(state.libraries as any);
          }
          if (Array.isArray(state.duplicateWords)) {
            state.duplicateWords = new Map(state.duplicateWords as any);
          }
          if (Array.isArray(state.wordHighlightColors)) {
            state.wordHighlightColors = new Map(state.wordHighlightColors as any);
          }
          if (Array.isArray(state.usedHighlightColors)) {
            state.usedHighlightColors = new Set(state.usedHighlightColors as any);
          }
        }
      }
    }
  )
);

// ===== 创建填词模式Store =====

export const useWordInputStore = create<WordInputStore>((set, get) => ({
  // 初始状态
  isActive: false,
  selectedCell: null,
  matchedLibrary: null,
  selectedWordIndex: 0,
  availableWords: [],

  activateWordInput: (x: number, y: number, color: BasicColorType, level: DataLevel) => {
    const libraryKey = createWordLibraryKey(color, level);
    const wordLibraryStore = useWordLibraryStore.getState();
    const library = wordLibraryStore.getLibrary(libraryKey);

    set({
      isActive: true,
      selectedCell: { x, y },
      matchedLibrary: libraryKey,
      selectedWordIndex: 0,
      availableWords: library?.words || []
    });
  },

  deactivateWordInput: () => {
    set({
      isActive: false,
      selectedCell: null,
      matchedLibrary: null,
      selectedWordIndex: 0,
      availableWords: []
    });
  },

  selectNextWord: () => {
    const state = get();
    if (state.availableWords.length > 0) {
      const nextIndex = (state.selectedWordIndex + 1) % state.availableWords.length;
      set({ selectedWordIndex: nextIndex });
    }
  },

  selectPreviousWord: () => {
    const state = get();
    if (state.availableWords.length > 0) {
      const prevIndex = state.selectedWordIndex === 0
        ? state.availableWords.length - 1
        : state.selectedWordIndex - 1;
      set({ selectedWordIndex: prevIndex });
    }
  },

  confirmWordSelection: () => {
    const state = get();
    if (state.availableWords.length > 0 && state.selectedWordIndex < state.availableWords.length) {
      const selectedWord = state.availableWords[state.selectedWordIndex];

      // 更新词语使用统计
      if (state.matchedLibrary && state.selectedCell) {
        // 这里可以添加使用统计更新逻辑
        // const wordLibraryStore = useWordLibraryStore.getState();
      }

      return selectedWord;
    }
    return null;
  },

  clearCellWord: () => {
    // 清除单元格词语的逻辑
    // 这里需要与矩阵状态管理集成
  }
}));
